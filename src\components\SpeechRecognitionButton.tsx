import React, { useState, useEffect } from 'react';
import { Button, Tooltip, Badge, message } from 'antd';
import { AudioOutlined, LoadingOutlined } from '@ant-design/icons';
import { speechRecognitionService, type CountdownInfo } from '../services/speechService';

interface SpeechRecognitionButtonProps {
  onTranscript: (text: string) => void;
  onInterimResult?: (text: string, isReplace?: boolean) => void; // 新增：临时结果回调，包含是否替换信息
  onStartListening?: () => void; // 新增：开始监听回调
  onEndListening?: () => void; // 新增：结束监听回调
  tempText?: string; // 新增：外部传入的临时文字，用于按钮右侧显示
  className?: string;
}

const SpeechRecognitionButton: React.FC<SpeechRecognitionButtonProps> = ({
  onTranscript,
  onInterimResult,
  onStartListening,
  onEndListening,
  tempText = '',
  className = ''
}) => {
  const [isSupported, setIsSupported] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isPreparing, setIsPreparing] = useState(false);
  const [countdownInfo, setCountdownInfo] = useState<CountdownInfo | null>(null);
  const [hasSpeechDetected, setHasSpeechDetected] = useState(false);

  useEffect(() => {
    setIsSupported(speechRecognitionService.isRecognitionSupported());
  }, []);

  const handleStartListening = async () => {
    if (isListening || isPreparing) {
      // 如果正在监听或准备中，点击停止
      speechRecognitionService.stopListening();
      setIsListening(false);
      setIsPreparing(false);
      setCountdownInfo(null);
      setHasSpeechDetected(false);
      return;
    }

    try {
      await speechRecognitionService.startListening(
        (result) => {
          // 只要收到结果就表示检测到了语音
          setHasSpeechDetected(true);
          
          if (result.isFinal) {
            // 最终结果 - 调用onTranscript回调
            onTranscript(result.transcript);
          } else {
            // 临时结果 - 只调用onInterimResult回调，不设置内部tempResult
            // 因为外部会通过tempText传入用于按钮右侧显示的文字
            onInterimResult?.(result.transcript, result.isReplace);
          }
        },
        (error) => {
          message.error(error);
          setIsListening(false);
          setIsPreparing(false);
          setCountdownInfo(null);
          setHasSpeechDetected(false);
        },
        () => {
          // 识别结束
          setIsListening(false);
          setIsPreparing(false);
          setCountdownInfo(null);
          setHasSpeechDetected(false);
          onEndListening?.(); // 通知外部识别结束，但不清理状态
        },
        () => {
          // 准备中状态
          setIsPreparing(true);
          setIsListening(false);
          setCountdownInfo(null);
          setHasSpeechDetected(false);
        },
        () => {
          // 开始监听状态
          setIsPreparing(false);
          setIsListening(true);
          setHasSpeechDetected(false); // 开始监听时重置语音检测状态
          onStartListening?.(); // 通知外部开始监听
        },
        {
          language: 'zh-CN',
          continuous: true,
          interimResults: true,
          noSpeechTimeout: 5000 // 5秒无语音超时
        },
        (countdown) => {
          // 倒计时回调
          setCountdownInfo(countdown);
          // 当开始倒计时时，说明没有检测到语音，重置语音检测状态
          if (countdown) {
            setHasSpeechDetected(false);
          }
        }
      );
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      message.error('启动语音识别失败');
      setIsListening(false);
      setIsPreparing(false);
      setCountdownInfo(null);
      setHasSpeechDetected(false);
    }
  };

  // 获取状态文本
  const getStatusText = () => {
    if (isPreparing) {
      return '准备中';
    }
    if (isListening) {
      if (countdownInfo) {
        return (
          <span>
            识别中：没有检测到声音，将在
            <strong style={{ fontWeight: 'bold', color: '#ff4d4f' }}>
              {countdownInfo.remainingSeconds}
            </strong>
            秒后结束！
          </span>
        );
      }
      // 使用外部传入的tempText用于按钮右侧显示
      if (tempText) {
        return `识别中：${tempText}`;
      }
      return '识别中';
    }
    return '';
  };

  // 获取tooltip文本
  const getTooltipText = () => {
    if (isListening || isPreparing) {
      return '结束语音输入';
    }
    return '语音输入';
  };

  // 动态生成按钮类名
  const getButtonClass = () => {
    let buttonClass = `scene-function-button speech-recognition-button ${className}`;
    
    if (isPreparing) {
      buttonClass += ' preparing';
    } else if (isListening) {
      buttonClass += ' listening';
    }
    
    return buttonClass;
  };

  if (!isSupported) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <Tooltip title="浏览器不支持语音识别">
          <Button 
            className={`scene-function-button ${className}`}
            disabled
            icon={<AudioOutlined style={{ color: '#cccccc' }} />}
          />
        </Tooltip>
      </div>
    );
  }

  const statusText = getStatusText();

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      <Tooltip title={getTooltipText()}>
        <Badge 
          dot={isListening && hasSpeechDetected} 
          color="#52c41a"
        >
          <Button
            className={getButtonClass()}
            onClick={handleStartListening}
            icon={
              isPreparing ? (
                <LoadingOutlined />
              ) : (
                <AudioOutlined />
              )
            }
          />
        </Badge>
      </Tooltip>
      {statusText && (
        <div style={{ 
          fontSize: '12px', 
          color: '#666',
          whiteSpace: 'nowrap',
          maxWidth: countdownInfo ? '300px' : (tempText ? '200px' : '120px'), // 有临时文字时增加宽度
          overflow: 'hidden',
          textOverflow: 'ellipsis'
        }}>
          {statusText}
        </div>
      )}
    </div>
  );
};

export default SpeechRecognitionButton;